import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../../../../../utlis/constants/colors.dart';
import '../../../../../utlis/constants/size.dart';
import '../../../../../common/widgets/Button/primarybutton.dart';
import '../../../../../utlis/app_config/app_config.dart';

class RequestPasswordResetScreen extends StatefulWidget {
  @override
  State<RequestPasswordResetScreen> createState() => _RequestPasswordResetScreenState();
}

class _RequestPasswordResetScreenState extends State<RequestPasswordResetScreen> {
  final TextEditingController emailController = TextEditingController();
  bool isLoading = false;

  Future<void> sendPasswordResetRequest() async {
    final email = emailController.text.trim();
    if (email.isEmpty || !email.contains('@')) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text("Please enter a valid email")));
      return;
    }

    setState(() => isLoading = true);
    final url = Uri.parse('${AppConfig.baseUrl}/auth/request-password-reset');

    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: json.encode({"email": email}),
    );

    setState(() => isLoading = false);

    if (response.statusCode == 200) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text("Reset link sent to your email")));
      Navigator.pop(context); // Back to Login screen
    } else {
      final error = jsonDecode(response.body);
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(error['message'] ?? "Something went wrong")));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("Reset Password")),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            TextFormField(
              controller: emailController,
              keyboardType: TextInputType.emailAddress,
              decoration: InputDecoration(
                labelText: "Email",
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 20),
            PrimaryButton(
              onPressed: isLoading ? null : sendPasswordResetRequest,
              title: isLoading ? "Sending..." : "Send Reset Link",
            ),
          ],
        ),
      ),
    );
  }
}
